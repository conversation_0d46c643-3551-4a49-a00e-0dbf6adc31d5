#!/bin/bash

# CosyVoice 服务器启动脚本

# 设置变量
CONDA_ENV="cosyvoice"
SERVER_PORT=50000
LOG_FILE="server.log"
PID_FILE="server.pid"

# 函数：启动服务器
start_server() {
    echo "启动 CosyVoice 服务器..."
    
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "服务器已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    # 激活 conda 环境并启动服务器
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate $CONDA_ENV

    # 加载 .env 文件中的环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
        echo "已加载 .env 文件中的环境变量"
    fi

    nohup python server/main.py --port $SERVER_PORT > $LOG_FILE 2>&1 &
    echo $! > $PID_FILE
    
    echo "服务器已启动 (PID: $!)"
    echo "端口: $SERVER_PORT"
    echo "日志文件: $LOG_FILE"
    echo "访问地址: http://localhost:$SERVER_PORT"
}

# 函数：停止服务器
stop_server() {
    echo "停止 CosyVoice 服务器..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            echo "服务器已停止 (PID: $PID)"
        else
            echo "服务器进程不存在"
        fi
        rm -f "$PID_FILE"
    else
        echo "PID 文件不存在，尝试通过进程名停止..."
        pkill -f "python server/main.py"
    fi
}

# 函数：查看状态
status_server() {
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "服务器正在运行 (PID: $(cat $PID_FILE))"
        echo "端口: $SERVER_PORT"
        echo "日志文件: $LOG_FILE"
    else
        echo "服务器未运行"
    fi
}

# 函数：查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo "显示最近的日志..."
        tail -f "$LOG_FILE"
    else
        echo "日志文件不存在"
    fi
}

# 主逻辑
case "$1" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        stop_server
        sleep 2
        start_server
        ;;
    status)
        status_server
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务器"
        echo "  stop    - 停止服务器"
        echo "  restart - 重启服务器"
        echo "  status  - 查看服务器状态"
        echo "  logs    - 查看服务器日志"
        exit 1
        ;;
esac
