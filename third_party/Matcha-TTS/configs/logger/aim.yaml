# https://aimstack.io/

# example usage in lightning module:
# https://github.com/aimhubio/aim/blob/main/examples/pytorch_lightning_track.py

# open the Aim UI with the following command (run in the folder containing the `.aim` folder):
# `aim up`

aim:
  _target_: aim.pytorch_lightning.AimLogger
  repo: ${paths.root_dir} # .aim folder will be created here
  # repo: "aim://ip_address:port" # can instead provide IP address pointing to Aim remote tracking server which manages the repo, see https://aimstack.readthedocs.io/en/latest/using/remote_tracking.html#

  # aim allows to group runs under experiment name
  experiment: null # any string, set to "default" if not specified

  train_metric_prefix: "train/"
  val_metric_prefix: "val/"
  test_metric_prefix: "test/"

  # sets the tracking interval in seconds for system usage metrics (CPU, GPU, memory, etc.)
  system_tracking_interval: 10 # set to null to disable system metrics tracking

  # enable/disable logging of system params such as installed packages, git info, env vars, etc.
  log_system_params: true

  # enable/disable tracking console logs (default value is true)
  capture_terminal_logs: false # set to false to avoid infinite console log loop issue https://github.com/aimhubio/aim/issues/2550
