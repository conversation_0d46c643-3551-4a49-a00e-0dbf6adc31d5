Sliding Window Attention is enabled but not implemented for `sdpa`; unexpected results may be encountered.
/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/diffusers/models/lora.py:393: FutureWarning: `LoRACompatibleLinear` is deprecated and will be removed in version 1.0.0. Use of `LoRACompatibleLinear` is deprecated. Please switch to PEFT backend by installing PEFT: `pip install peft`.
  deprecate("LoRACompatibleLinear", "1.0.0", deprecation_message)
2025-08-18 14:35:48,102 INFO input frame rate=25
/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/torch/nn/utils/weight_norm.py:28: UserWarning: torch.nn.utils.weight_norm is deprecated in favor of torch.nn.utils.parametrizations.weight_norm.
  warnings.warn("torch.nn.utils.weight_norm is deprecated in favor of torch.nn.utils.parametrizations.weight_norm.")
2025-08-18 14:35:49,657 ERROR Failed to load CosyVoice2 model: [Errno 13] Permission denied: '/Users/<USER>/.cache/modelscope/hub/pengzhendong/wetext/.msc'
failed to import ttsfrd, use wetext instead
Traceback (most recent call last):
  File "/Users/<USER>/Work/Code/AI/CosyVoice/server/main.py", line 49, in <module>
    main()
  File "/Users/<USER>/Work/Code/AI/CosyVoice/server/main.py", line 42, in main
    app = create_app(model_dir=args.model_dir)
  File "/Users/<USER>/Work/Code/AI/CosyVoice/server/fastapi_app.py", line 76, in create_app
    handler = CosyVoice2Handler(model_dir)
  File "/Users/<USER>/Work/Code/AI/CosyVoice/server/cosyvoice2_handler.py", line 21, in __init__
    self.cosyvoice = CosyVoice2(model_dir=model_dir, load_jit=False, load_trt=False, load_vllm=False, fp16=False)
  File "/Users/<USER>/Work/Code/AI/CosyVoice/cosyvoice/cli/cosyvoice.py", line 156, in __init__
    self.frontend = CosyVoiceFrontEnd(configs['get_tokenizer'],
  File "/Users/<USER>/Work/Code/AI/CosyVoice/cosyvoice/cli/frontend.py", line 71, in __init__
    self.zh_tn_model = ZhNormalizer(remove_erhua=False)
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/wetext/wetext.py", line 38, in __init__
    repo_dir = snapshot_download("pengzhendong/wetext")
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/snapshot_download.py", line 85, in snapshot_download
    return _snapshot_download(
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/snapshot_download.py", line 195, in _snapshot_download
    temporary_cache_dir, cache = create_temporary_directory_and_cache(
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/file_download.py", line 303, in create_temporary_directory_and_cache
    cache = ModelFileSystemCache(cache_dir, group_or_owner, name)
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/utils/caching.py", line 138, in __init__
    super().__init__(os.path.join(cache_root, owner, name))
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/utils/caching.py", line 38, in __init__
    self.load_cache()
  File "/opt/miniconda3/envs/cosyvoice/lib/python3.10/site-packages/modelscope/hub/utils/caching.py", line 48, in load_cache
    with open(cache_keys_file_path, 'rb') as f:
PermissionError: [Errno 13] Permission denied: '/Users/<USER>/.cache/modelscope/hub/pengzhendong/wetext/.msc'
