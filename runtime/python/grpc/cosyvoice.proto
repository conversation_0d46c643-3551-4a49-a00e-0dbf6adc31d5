syntax = "proto3";

package cosyvoice;
option go_package = "protos/";

service CosyVoice{
  rpc Inference(Request) returns (stream Response) {}
}

message Request{
  oneof RequestPayload {
    sftRequest sft_request = 1;
    zeroshotRequest zero_shot_request = 2;
    crosslingualRequest cross_lingual_request = 3;
    instructRequest instruct_request = 4;
  }
}

message sftRequest{
  string spk_id = 1;
  string tts_text = 2;
}

message zeroshotRequest{
  string tts_text = 1;
  string prompt_text = 2;
  bytes prompt_audio = 3;
}

message crosslingualRequest{
  string tts_text = 1;
  bytes prompt_audio = 2;
}

message instructRequest{
  string tts_text = 1;
  string spk_id = 2;
  string instruct_text = 3;
}

message Response{
  bytes tts_audio = 1;
}