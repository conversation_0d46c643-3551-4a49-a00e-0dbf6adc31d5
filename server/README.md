# CosyVoice2 服务端使用说明

## 1. zero_shot API 说明

- **接口路径**：`/inference`
- **请求方法**：POST
- **Content-Type**：`application/x-www-form-urlencoded`
- **参数**：
  - `text`（string，必填）：待合成的文本内容
  - `voice_id`（string，必填）：音色标识（对应 VOICE_CONFIG 字典的 key）
- **返回**：
  - `audio_url`（string）：合成音频的下载链接

示例请求（Python）：
```python
import requests

resp = requests.post(
    "http://localhost:50000/inference",
    data={"text": "你好，世界", "voice_id": "voice1"}
)
print(resp.json())  # {"audio_url": "..."}
```

```shell
curl -X POST "http://localhost:50000/inference" \
  -F "text=你好，师傅！" \
  -F "voice_id=sun_wukong_1"
```

## 2. 依赖安装

请先安装 Python 依赖：
```bash
pip install -r requirements.txt
```

## 3. 服务启动

在项目根目录下运行以下命令启动服务（默认端口 50000）：
```bash
python server/main.py
```
如需自定义端口或模型路径，可使用参数：
```bash
python server/main.py --port 50000 --model_dir pretrained_models/CosyVoice2-0.5B
```

### 启动后台服务
```
chmod +x start_server.sh
./start_server.sh start
```

## 4. VOICE_CONFIG 配置说明

- 音色配置通过 VOICE_CONFIG 字典进行管理，结构为：
  ```python
  VOICE_CONFIG = {
      "voice1": {
          "path": "asset/voice1.wav",
          "prompt": "希望你以后能够做的比我还好呦。"
      },
      "voice2": {
          "path": "asset/voice2.wav",
          "prompt": "加油，未来属于你！"
      }
      # 可继续添加更多 voice
  }
  ```
- 每个 voice_id 对应一个 wav 文件和提示文本，系统启动时自动加载 VOICE_CONFIG 中所有音色，无需手动遍历 asset/ 目录。
- 支持 wav 格式，采样率自动转换为 16k。
- API 调用时通过 voice_id 选择音色和提示文本。
- 语音合成完成后直接保存为 wav 格式，无需转换为 mp3。