import os
import sys
import uuid
import subprocess
import torchaudio
from fastapi import FastAPI, Form
from fastapi.responses import JSONResponse
from minio import Minio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加当前目录到Python路径以便导入同目录下的模块
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加Matcha-TTS到Python路径
matcha_path = os.path.join(project_root, 'third_party', 'Matcha-TTS')
if matcha_path not in sys.path:
    sys.path.insert(0, matcha_path)

from cosyvoice2_handler import CosyVoice2Handler
from cosyvoice.utils.file_utils import load_wav


ASSET_DIR = os.path.join(os.path.dirname(__file__), "../asset")
VOICE_SR = 16000

VOICE_CONFIG = {
    "tang_seng_1": {
        "path": "asset/（唐僧）弟子是东土大唐驾下差来，上西天拜佛求经去的.wav",
        "prompt": "弟子是东土大唐驾下差来，上西天拜佛求经去的"
    },
    "sun_wukong_1": {
        "path": "asset/（孙悟空）嘿嘿！我乃五百年前大闹天宫的齐天大圣.wav",
        "prompt": "嘿嘿！我乃五百年前大闹天宫的齐天大圣"
    },
    "sun_wukong_2": {
        "path": "asset/（孙悟空）见了小的要叫姑娘，见了老的要叫奶奶.wav",
        "prompt": "见了小的要叫姑娘，见了老的要叫奶奶"
    },
    "sha_seng_1": {
        "path": "asset/（沙僧）师傅被那妖怪给捉去了.wav",
        "prompt": "师傅被那妖怪给捉去了"
    },
    "zhu_bajie_1": {
        "path": "asset/（猪八戒）看你的得意地，一听说抓妖怪就跟见了你外公似的.wav",
        "prompt": "看你的得意地，一听说抓妖怪就跟见了你外公似的"
    },
    "hong_haier_1": {
        "path": "asset/（红孩儿）你是猴子请来的救兵嘛.wav",
        "prompt": "你是猴子请来的救兵嘛"
    }
}

def load_voices(voice_config):
    voices = {}
    for voice_id, info in voice_config.items():
        path = info["path"]
        if not os.path.exists(path):
            continue

        waveform = load_wav(path, VOICE_SR)
        voices[voice_id] = waveform
    return voices

def create_app(model_dir):
    app = FastAPI()
    handler = CosyVoice2Handler(model_dir)
    minio_client = Minio(
        endpoint=os.getenv("MINIO_ENDPOINT", "172.16.10.101:9000"),
        access_key=os.getenv("MINIO_ACCESS_KEY", "PVGLiSP4K3hVAHjb2hv2"),
        secret_key=os.getenv("MINIO_SECRET_KEY", "k1AoVccJaauLYVk60nYo8QNmisAMHKx4uhXQseSE"),
        secure=False
    )
    minio_bucket = os.getenv("MINIO_BUCKET", "aiwb")
    if not minio_client.bucket_exists(minio_bucket):
        minio_client.make_bucket(minio_bucket)

    voice_dict = load_voices(VOICE_CONFIG)

    @app.post("/inference")
    async def inference(
        text: str = Form(...),
        voice_id: str = Form(...)
    ):
        if voice_id not in voice_dict or voice_id not in VOICE_CONFIG:
            return JSONResponse(status_code=400, content={"error": "Invalid voice_id"})
        prompt_speech = voice_dict[voice_id]
        prompt_text = VOICE_CONFIG[voice_id]["prompt"]
        result = handler.inference_zero_shot(
            tts_text=text,
            prompt_text=prompt_text,
            prompt_speech_16k=prompt_speech,
            speed=1.0
        )
        if result is None:
            return JSONResponse(status_code=500, content={"error": "TTS generation failed"})
        tts_speech = result["tts_speech"]
        audio_id = str(uuid.uuid4())

        print(result)

        # 先保存为WAV格式
        wav_path = f"/tmp/{audio_id}.wav"
        torchaudio.save(wav_path, tts_speech, handler.cosyvoice.sample_rate)

        # 使用ffmpeg转换为MP3
        mp3_path = f"/tmp/{audio_id}.mp3"
        try:
            # 使用ffmpeg进行WAV到MP3的转换
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # -y 表示覆盖输出文件
                '-i', wav_path,  # 输入文件
                '-codec:a', 'libmp3lame',  # 使用LAME MP3编码器
                '-b:a', '128k',  # 设置比特率为128kbps
                '-ar', str(handler.cosyvoice.sample_rate),  # 设置采样率
                mp3_path  # 输出文件
            ]

            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, check=True)
            print(f"FFmpeg conversion successful: {wav_path} -> {mp3_path}")

            # 上传MP3文件到MinIO
            with open(mp3_path, "rb") as f:
                minio_client.put_object(
                    minio_bucket,
                    f"{audio_id}.mp3",
                    f,
                    length=os.path.getsize(mp3_path),
                    content_type="audio/mpeg"
                )

            # 清理临时文件
            os.remove(wav_path)
            os.remove(mp3_path)

            # 生成MinIO URL
            endpoint = os.getenv("MINIO_ENDPOINT", "localhost:9000")
            minio_url = f"http://{endpoint}/{minio_bucket}/{audio_id}.mp3"

        except subprocess.CalledProcessError as e:
            print(f"FFmpeg conversion failed: {e}")
            print(f"FFmpeg stderr: {e.stderr}")

            # 如果MP3转换失败，回退到WAV格式
            with open(wav_path, "rb") as f:
                minio_client.put_object(
                    minio_bucket,
                    f"{audio_id}.wav",
                    f,
                    length=os.path.getsize(wav_path),
                    content_type="audio/wav"
                )

            os.remove(wav_path)
            endpoint = os.getenv("MINIO_ENDPOINT", "localhost:9000")
            minio_url = f"http://{endpoint}/{minio_bucket}/{audio_id}.wav"

        except FileNotFoundError:
            print("FFmpeg not found, falling back to WAV format")

            # 如果ffmpeg不存在，回退到WAV格式
            with open(wav_path, "rb") as f:
                minio_client.put_object(
                    minio_bucket,
                    f"{audio_id}.wav",
                    f,
                    length=os.path.getsize(wav_path),
                    content_type="audio/wav"
                )

            os.remove(wav_path)
            endpoint = os.getenv("MINIO_ENDPOINT", "localhost:9000")
            minio_url = f"http://{endpoint}/{minio_bucket}/{audio_id}.wav"
        return JSONResponse(content={"audio_url": minio_url})

    return app
