import os
import sys
import argparse
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加当前目录到Python路径以便导入同目录下的模块
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加Matcha-TTS到Python路径
matcha_path = os.path.join(project_root, 'third_party', 'Matcha-TTS')
if matcha_path not in sys.path:
    sys.path.insert(0, matcha_path)

from fastapi_app import create_app

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--port',
                        type=int,
                        default=50000,
                        help='server port')
    parser.add_argument('--model_dir',
                        type=str,
                        default='pretrained_models/CosyVoice2-0.5B',
                        help='local path or modelscope repo id')
    parser.add_argument('--host',
                        type=str,
                        default='0.0.0.0',
                        help='server host')
    args = parser.parse_args()

    app = create_app(model_dir=args.model_dir)
    
    import uvicorn
    uvicorn.run(app, host=args.host, port=args.port)


if __name__ == '__main__':
    main()