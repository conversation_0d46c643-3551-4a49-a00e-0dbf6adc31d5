[Unit]
Description=CosyVoice Server
After=network.target

[Service]
Type=simple
User=zzy
Group=zzy
WorkingDirectory=/home/<USER>/Program/code/CosyVoice
Environment=PATH=/home/<USER>/miniconda3/envs/cosyvoice/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/home/<USER>/miniconda3/envs/cosyvoice/bin/python server/main.py --port 8000
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
